import { Hono } from 'hono'
import { cors } from 'hono/cors'
import { logger } from 'hono/logger'
import { prettyJSON } from 'hono/pretty-json'

import { compress } from 'hono/compress'
import { timing } from 'hono/timing'
import { requestId } from 'hono/request-id'

// Import middleware
import {
  error<PERSON><PERSON><PERSON>,
  notFoundHandler,
  databaseMiddleware,
  dbHealthMiddleware,
  dbMetricsMiddleware,
  securityHeaders,
  securityLogger,
  requestTimeout,
  validateRateLimit,
  sanitizeInput,
  cloudflareBindingsMiddleware,
  kvMiddleware,
  cfEnvMiddleware,
  cacheMiddleware,
  analyticsMiddleware,
} from './middleware'

// Import route handlers (will be added when implementing specific features)

// Type definitions for Cloudflare Workers bindings
export type Bindings = {
  // D1 Database
  DB: D1Database
  
  // KV Namespaces
  CACHE: KVNamespace
  ANALYTICS: KVNamespace
  CONFIG: KVNamespace
  
  // Environment variables
  NODE_ENV: string
  APP_NAME: string
  APP_VERSION: string
  API_TIMEOUT: string
  CACHE_TTL: string
  ENABLE_ANALYTICS: string
  ENABLE_CACHING: string
  ENABLE_DEBUG: string
  RATE_LIMIT_REQUESTS: string
  RATE_LIMIT_WINDOW: string
  LOG_LEVEL: string
  LOG_FORMAT: string
  
  // Secrets (set via wrangler secret)
  JWT_SECRET: string
  ACCESSTRADE_API_KEY: string
  ACCESSTRADE_API_SECRET: string
}

// Variables that can be set in context
export interface Variables {
  requestId: string
  userId?: string
  userRole?: 'user' | 'admin'
  startTime: number
  // Database-related variables
  db?: any
  rawDb?: D1Database
  dbHealth?: any
  dbMetrics?: any
  transaction?: any
  // Security-related variables
  sanitizedBody?: any
}

// Create main Hono app with type safety
const app = new Hono<{
  Bindings: Bindings
  Variables: Variables
}>()

// Global middleware stack (order matters!)
// 1. Request identification and timing
app.use('*', requestId())
app.use('*', timing())

// 2. Security middleware
app.use('*', securityHeaders({
  contentSecurityPolicy: "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'",
  frameOptions: 'DENY',
  contentTypeOptions: true,
  referrerPolicy: 'strict-origin-when-cross-origin'
}))
app.use('*', securityLogger())
app.use('*', requestTimeout(30000))

// 3. Input sanitization and rate limiting
app.use('*', sanitizeInput())
app.use('/api/*', validateRateLimit(100, 60000)) // 100 requests per minute

// 4. Logging and formatting
app.use('*', logger())
app.use('*', compress())
app.use('*', prettyJSON())

// 5. Cloudflare Workers bindings
app.use('*', cloudflareBindingsMiddleware())
app.use('*', cfEnvMiddleware())
app.use('/api/*', kvMiddleware('CACHE'))
app.use('/api/*', analyticsMiddleware())

// 6. Database middleware for API routes
app.use('/api/*', databaseMiddleware())
app.use('/api/*', dbHealthMiddleware())
app.use('/api/*', dbMetricsMiddleware())

// 7. Caching for GET requests
app.use('/api/*', cacheMiddleware({
  ttl: 300, // 5 minutes
  skipMethods: ['POST', 'PUT', 'DELETE', 'PATCH'],
  skipPaths: ['/api/auth', '/api/admin'],
}))

// CORS configuration
app.use('/api/*', cors({
  origin: ['http://localhost:3000', 'http://localhost:3001', 'https://*.workers.dev', 'https://*.pages.dev'],
  allowHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  exposeHeaders: ['X-Request-ID', 'Server-Timing'],
  maxAge: 86400,
  credentials: true,
}))

// Health check endpoint
app.get('/health', (c) => {
  return c.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    version: c.env.APP_VERSION || '1.0.0',
    environment: c.env.NODE_ENV || 'development',
    requestId: c.get('requestId'),
  })
})

// Simple test endpoint (for direct access)
app.get('/api/test', (c) => {
  return c.json({
    success: true,
    message: 'Hono API is working',
    timestamp: new Date().toISOString(),
  })
})

// Test endpoint for TanStack Start bridge (path without /api prefix)
app.get('/test', (c) => {
  return c.json({
    success: true,
    message: 'TanStack Start API bridge is working!',
    timestamp: new Date().toISOString(),
    bridge: 'active',
  })
})

// Simple database test endpoint
app.get('/api/database/health', async (c) => {
  try {
    if (!c.env.DB) {
      return c.json({
        error: 'Database binding not found',
        message: 'D1 database is not properly configured'
      }, 503)
    }

    const startTime = Date.now()
    await c.env.DB.prepare('SELECT 1').first()
    const latency = Date.now() - startTime

    return c.json({
      success: true,
      message: 'Database health check completed',
      data: {
        healthy: true,
        latency
      },
      timestamp: new Date().toISOString(),
    })
  } catch (error) {
    return c.json({
      error: 'Health check failed',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, 500)
  }
})

// Simple database stats endpoint
app.get('/api/database/stats', async (c) => {
  try {
    if (!c.env.DB) {
      return c.json({
        error: 'Database binding not found',
        message: 'D1 database is not properly configured'
      }, 503)
    }

    // Get table counts using raw SQL
    const userCount = await c.env.DB.prepare('SELECT COUNT(*) as count FROM users').first() as { count: number } | null
    const categoryCount = await c.env.DB.prepare('SELECT COUNT(*) as count FROM categories').first() as { count: number } | null

    const userTotal = userCount?.count || 0
    const categoryTotal = categoryCount?.count || 0

    return c.json({
      success: true,
      message: 'Database statistics retrieved',
      data: {
        tables: {
          users: userTotal,
          categories: categoryTotal,
        },
        total: userTotal + categoryTotal,
      },
      timestamp: new Date().toISOString(),
    })
  } catch (error) {
    return c.json({
      error: 'Failed to get statistics',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, 500)
  }
})

// Import API routes
import authRoutes from './routes/auth'
import couponsRoutes from './routes/coupons'
import productsRoutes from './routes/products'
import campaignsRoutes from './routes/campaigns'
import analyticsRoutes from './routes/analytics'
import adminRoutes from './routes/admin'

// API routes (without /api prefix since bridge removes it)
const routes = app
  .route('/auth', authRoutes)
  .route('/coupons', couponsRoutes)
  .route('/products', productsRoutes)
  .route('/campaigns', campaignsRoutes)
  .route('/analytics', analyticsRoutes)
  .route('/admin', adminRoutes)

// 404 handler
app.notFound(notFoundHandler())

// Global error handler
app.onError(errorHandler())

export default app
export type AppType = typeof routes
